import { AuthService } from '../services/auth.service';
import { LoggerService } from '../interceptors/logger.service';


export function appInitializer(authService: AuthService) {
  const logger: LoggerService = LoggerService.createLogger('appInitializer');

  return () => {
    logger.debug('app initializer - deferring token refresh to improve startup performance');

    // Defer token refresh to after app loads to improve initial load time
    setTimeout(() => {
      logger.debug('performing deferred token refresh');
      authService.refreshToken().subscribe({
        next: (result) => logger.debug('deferred token refresh completed', result),
        error: (error) => logger.debug('deferred token refresh failed', error)
      });
    }, 100);

    // Return resolved promise immediately to not block app startup
    return Promise.resolve();
  };
}
