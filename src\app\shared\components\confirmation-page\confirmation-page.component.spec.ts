import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';

import { ConfirmationPageComponent } from './confirmation-page.component';
import { BookingAPIService } from '../../services/booking-api.service';
import { PaymentResponseService } from '../../services/payment-response.service';
import { LoggerService } from '../../interceptors/logger.service';
import { GoogleMapsService } from '../../services/google-maps/google-maps.service';
import { PaymentOption } from '../../enums/payment-option.enum';

describe('ConfirmationPageComponent', () => {
  let component: ConfirmationPageComponent;
  let fixture: ComponentFixture<ConfirmationPageComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;
  let mockBookingAPIService: jasmine.SpyObj<BookingAPIService>;
  let mockPaymentResponseService: jasmine.SpyObj<PaymentResponseService>;
  let mockLoggerService: jasmine.SpyObj<LoggerService>;
  let mockGoogleMapsService: jasmine.SpyObj<GoogleMapsService>;

  beforeEach(async(() => {
    // Create spy objects
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      params: of({ id: 'test-booking-id' }),
      snapshot: { params: { id: 'test-booking-id' } }
    };
    mockBookingAPIService = jasmine.createSpyObj('BookingAPIService', ['getBookingReceipt']);
    mockPaymentResponseService = jasmine.createSpyObj('PaymentResponseService', ['bookingRequestData', 'paymentResponseValue']);
    mockLoggerService = jasmine.createSpyObj('LoggerService', ['debug', 'error', 'trace']);
    mockGoogleMapsService = jasmine.createSpyObj('GoogleMapsService', ['loadGoogleMaps']);

    TestBed.configureTestingModule({
      declarations: [ ConfirmationPageComponent ],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: BookingAPIService, useValue: mockBookingAPIService },
        { provide: PaymentResponseService, useValue: mockPaymentResponseService },
        { provide: LoggerService, useValue: mockLoggerService },
        { provide: GoogleMapsService, useValue: mockGoogleMapsService }
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ConfirmationPageComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('mapApiResponseToBookingData', () => {
    it('should correctly map partial payment amount for partial payments', () => {
      const apiData = {
        bookingID: 'CY-080825-35434',
        basicFare: 24115.00,
        gst: 1205.75,
        fare: 25321.00,
        paymentOption: PaymentOption.PARTIAL,
        partialPaymentAmount: 100.00,
        cashAmountToPayDriver: 25221,
        paymentStatus: 'Completed',
        travelerName: 'Mohammad Uzaif',
        phoneNumber: '1234567890',
        mailId: '<EMAIL>'
      };

      // Access the private method using bracket notation
      const result = (component as any).mapApiResponseToBookingData(apiData);

      expect(result.amountPaid).toBe(100.00); // Should use partialPaymentAmount
      expect(result.remainingAmountForDriver).toBe(25221);
      expect(result.paymentType).toBe('PARTIAL');
      expect(result.totalFare).toBe(25320.75); // basicFare + gst
    });

    it('should correctly map full payment amount for full payments', () => {
      const apiData = {
        bookingID: 'CY-080825-35435',
        basicFare: 24115.00,
        gst: 1205.75,
        fare: 25321.00,
        paymentOption: PaymentOption.FULL,
        partialPaymentAmount: null,
        cashAmountToPayDriver: 0,
        paymentStatus: 'Completed',
        travelerName: 'Test User',
        phoneNumber: '1234567890',
        mailId: '<EMAIL>'
      };

      const result = (component as any).mapApiResponseToBookingData(apiData);

      expect(result.amountPaid).toBe(25321.00); // Should use fare for full payment
      expect(result.remainingAmountForDriver).toBe(0);
      expect(result.paymentType).toBe('FULL');
      expect(result.totalFare).toBe(25320.75); // basicFare + gst
    });

    it('should return 0 amountPaid when payment status is Unknown', () => {
      const apiData = {
        bookingID: 'CY-080825-35436',
        basicFare: 24115.00,
        gst: 1205.75,
        fare: 25321.00,
        paymentOption: PaymentOption.PARTIAL,
        partialPaymentAmount: 100.00,
        cashAmountToPayDriver: 25221,
        paymentStatus: 'Unknown',
        travelerName: 'Test User',
        phoneNumber: '1234567890',
        mailId: '<EMAIL>'
      };

      const result = (component as any).mapApiResponseToBookingData(apiData);

      expect(result.amountPaid).toBe(0); // Should be 0 for Unknown status
    });
  });
});
