import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { UserRidesComponent } from './user-rides.component';
import { BookingAPIService } from 'src/app/shared/services/booking-api.service';
import { LoggerService } from 'src/app/shared/interceptors/logger.service';
import { UserBooking } from 'src/app/shared/models/user-bookings.model';

describe('UserRidesComponent', () => {
  let component: UserRidesComponent;
  let fixture: ComponentFixture<UserRidesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ UserRidesComponent ],
      imports: [ HttpClientTestingModule, RouterTestingModule ],
      providers: [ BookingAPIService, LoggerService ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UserRidesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return correct status class for partial payment', () => {
    const partialBooking: UserBooking = {
      bookingId: 'CY-300725-29443',
      pickUpCity: 'Jaipur',
      dropOffCity: 'Ajmer',
      tripType: 'One Way',
      carCategory: 'SUV Premium',
      fare: 4200,
      pickUpAddress: 'Test Address',
      dropOffAddress: 'Test Address',
      pickUpDate: '2025-07-31T00:00:00',
      pickUpTime: '00:00',
      travelerName: 'Test User',
      phoneNumber: '**********',
      razorpayStatus: 'Paid',
      bookingDate: null,
      isPartialPayment: true,
      paidAmount: 1000
    };

    const statusClass = component.getStatusClass('Paid', partialBooking);
    expect(statusClass).toBe('status-partial');
  });

  it('should return correct status text for partial payment', () => {
    const partialBooking: UserBooking = {
      bookingId: 'CY-300725-29443',
      pickUpCity: 'Jaipur',
      dropOffCity: 'Ajmer',
      tripType: 'One Way',
      carCategory: 'SUV Premium',
      fare: 4200,
      pickUpAddress: 'Test Address',
      dropOffAddress: 'Test Address',
      pickUpDate: '2025-07-31T00:00:00',
      pickUpTime: '00:00',
      travelerName: 'Test User',
      phoneNumber: '**********',
      razorpayStatus: 'Paid',
      bookingDate: null,
      isPartialPayment: true,
      paidAmount: 1000
    };

    const statusText = component.getStatusText('Paid', partialBooking);
    expect(statusText).toBe('Partial Paid');
  });

  it('should return correct status class for full payment', () => {
    const fullBooking: UserBooking = {
      bookingId: 'CY-300725-64538',
      pickUpCity: 'Jaipur',
      dropOffCity: 'Pushkar',
      tripType: 'One Way',
      carCategory: 'SUV Premium',
      fare: 4200,
      pickUpAddress: 'Test Address',
      dropOffAddress: 'Test Address',
      pickUpDate: '2025-07-31T00:00:00',
      pickUpTime: '11:00',
      travelerName: 'Test User',
      phoneNumber: '**********',
      razorpayStatus: 'Paid',
      bookingDate: null,
      isPartialPayment: false,
      paidAmount: 4200
    };

    const statusClass = component.getStatusClass('Paid', fullBooking);
    expect(statusClass).toBe('status-paid');
  });

  it('should return correct status text for full payment', () => {
    const fullBooking: UserBooking = {
      bookingId: 'CY-300725-64538',
      pickUpCity: 'Jaipur',
      dropOffCity: 'Pushkar',
      tripType: 'One Way',
      carCategory: 'SUV Premium',
      fare: 4200,
      pickUpAddress: 'Test Address',
      dropOffAddress: 'Test Address',
      pickUpDate: '2025-07-31T00:00:00',
      pickUpTime: '11:00',
      travelerName: 'Test User',
      phoneNumber: '**********',
      razorpayStatus: 'Paid',
      bookingDate: null,
      isPartialPayment: false,
      paidAmount: 4200
    };

    const statusText = component.getStatusText('Paid', fullBooking);
    expect(statusText).toBe('Paid');
  });

  it('should return true for completed payment status', () => {
    expect(component.isPaymentCompleted('Paid')).toBe(true);
  });

  it('should return false for pending payment status', () => {
    expect(component.isPaymentCompleted('1')).toBe(false);
  });
});
